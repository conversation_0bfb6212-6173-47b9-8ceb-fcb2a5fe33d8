const {
  SlashCommandBuilder,
  PermissionFlagsBits,
  EmbedBuilder
} = require('discord.js');
const { saveModLogEntry } = require('./modlog.js');

const allowedRoleIds = ['1314078903522037802', '1374162168408309931'];
const logChannelId = '1384018163330715700';

module.exports = {
  data: new SlashCommandBuilder()
    .setName('kick')
    .setDescription('Kick a user from the server.')
    .addUserOption(option =>
      option.setName('user')
        .setDescription('The user to kick.')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('reason')
        .setDescription('Reason for the kick.')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('proof')
        .setDescription('Proof for the kick (link, image, etc.)')
        .setRequired(false))
    .setDefaultMemberPermissions(PermissionFlagsBits.KickMembers),

  async execute(interaction) {
    await interaction.deferReply({ ephemeral: true });

    const targetUser = interaction.options.getUser('user');
    const reason = interaction.options.getString('reason');
    const proof = interaction.options.getString('proof') || '';
    const executor = interaction.user;

    const memberRoles = interaction.member.roles.cache;
    const hasPermission = allowedRoleIds.some(roleId => memberRoles.has(roleId));

    if (!hasPermission) {
      return interaction.editReply({
        content: 'You do not have permission to use this command.'
      });
    }

    let member;
    try {
      member = await interaction.guild.members.fetch(targetUser.id);
    } catch {
      return interaction.editReply({ content: 'User not found in the server.' });
    }

    if (!member.kickable) {
      return interaction.editReply({
        content: 'I cannot kick this user due to their role being higher than mine or missing permissions.'
      });
    }

    // DM Embed
    const dmEmbed = new EmbedBuilder()
      .setTitle('You Have Been Kicked From Greenville Roleplay Corporation')
      .setColor('#A3A1AC')
      .setDescription(`You have been kicked from **${interaction.guild.name}** by **${executor.tag}**.`)
      .addFields({ name: 'Reason', value: reason })
      .setFooter({ text: 'If you have any questions, please contact one of the HRS.' });

    if (proof) {
      dmEmbed.addFields({ name: 'Proof', value: proof });
    }

    try {
      await targetUser.send({ embeds: [dmEmbed] });
    } catch {
      // Ignore DM failure
    }

    try {
      await member.kick(`${reason}${proof ? ` | Proof: ${proof}` : ''}`);

      // Log the kick action to modlog
      const caseId = saveModLogEntry({
        guildId: interaction.guild.id,
        targetUserId: targetUser.id,
        moderatorId: executor.id,
        action: 'kick',
        reason: reason,
        proof: proof || null
      });

      const logchannelEmbed = new EmbedBuilder()
        .setTitle('User Kicked')
        .setColor('#A3A1AC')
        .addFields(
          { name: 'User', value: `${targetUser.tag} (${targetUser.id})` },
          { name: 'Reason', value: reason },
          proof ? { name: 'Proof', value: proof } : null,
          { name: 'Case ID', value: `#${caseId}`, inline: true }
        ).filter(Boolean)
        .setFooter({ text: `Kicked by ${executor.tag}` });

      // Send to log channel
      try {
        const logChannel = interaction.guild.channels.cache.get(logChannelId);
        if (logChannel) {
          await logChannel.send({ embeds: [logchannelEmbed] });
        }
      } catch (error) {
        console.error('Failed to send kick log to channel:', error);
      }

      const confirmationEmbed = new EmbedBuilder()
        .setTitle('User Kicked Successfully')
        .setColor('#A3A1AC')
        .addFields(
          { name: 'User', value: `${targetUser.tag} (${targetUser.id})` },
          { name: 'Reason', value: reason },
          { name: 'Case ID', value: `#${caseId}`, inline: true }
        )
        .setFooter({ text: `Kicked by ${executor.tag}` });

      if (proof) {
        confirmationEmbed.addFields({ name: 'Proof', value: proof });
      }

      return interaction.editReply({ embeds: [confirmationEmbed] });

    } catch {
      return interaction.editReply({
        content: 'Failed to kick the user. Please check my permissions or role position.'
      });
    }
  },
};
