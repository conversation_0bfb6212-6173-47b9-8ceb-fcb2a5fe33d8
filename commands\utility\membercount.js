const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('membercount')
        .setDescription('Displays the number of members in the server'),
    
    async execute(interaction) {
        const guild = interaction.guild;
        const memberCount = guild.memberCount;

        const embed = new EmbedBuilder()
            .setTitle('Member Count')
            .setDescription(`${memberCount} members`)
            .setColor('#A3A1AC')
            .setFooter({
                text: 'Greenville Roleplay Server',
                iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1393035698684039288/Screenshot_2025-07-10_131042.png?ex=6871b517&is=68706397&hm=726b0bb828f8ae6d228f6cc5604e84516585f022987327a35e6fc6f771704105&'
            });
        
        await interaction.reply({
            embeds: [embed],
            ephemeral: false 
        });
    }
};
